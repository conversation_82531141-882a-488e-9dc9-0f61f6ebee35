// 网络请求相关类型定义

/**
 * 基础请求信息接口
 */
export interface RequestInfo {
  requestId: string
  url: string
  method: string
  type?: chrome.webRequest.ResourceType
  timeStamp: number
  tabId: number
  frameId: number
  initiator?: string
  requestHeaders?: chrome.webRequest.HttpHeader[]
  responseHeaders?: chrome.webRequest.HttpHeader[]
  statusCode?: number
  statusLine?: string
  pageTitle?: string
  pageUrl?: string
  favIconUrl?: string
}

/**
 * 过滤后的请求信息接口（包含额外的处理信息）
 */
export interface FilteredRequestInfo extends RequestInfo {
  domain: string
  timestamp: number
  duration: number
  error?: string
  status: 'completed' | 'failed'
  ext?: string
}

/**
 * 请求统计信息接口
 */
export interface RequestStats {
  totalRequests: number
  requestsByType: Record<string, number>
  requestsByDomain: Record<string, number>
  lastRequestTime: number
}

/**
 * 下载数据接口
 */
export interface DownloadData {
  requestId: string
  url: string
  filename: string
  requestHeaders?: chrome.webRequest.HttpHeader[]
  pageTitle?: string
  pageUrl?: string
  favIconUrl?: string
  type?: chrome.webRequest.ResourceType
  method: string
  size?: number
  ext?: string
  domain: string
}

/**
 * 过滤规则接口
 */
export interface FilterRule {
  ext?: string
  type?: string
  regex?: string
  blackList?: boolean
  enabled?: boolean
}

/**
 * 过滤选项接口
 */
export interface FilterOptions {
  Ext: FilterRule[]
  Type: FilterRule[]
  Regex: FilterRule[]
}

/**
 * 插件内部通信消息类型枚举
 * 用于 Background Script ↔ Popup/Sidepanel 通信
 */
export enum BackgroundMessageType {
  // 过滤请求管理
  GET_FILTERED_REQUESTS = "GET_FILTERED_REQUESTS", // 获取过滤请求列表
  CLEAR_FILTERED_REQUESTS = "CLEAR_FILTERED_REQUESTS", // 清空过滤请求列表
  CLEAR_FILTERED_REQUESTS_BY_TAB = "CLEAR_FILTERED_REQUESTS_BY_TAB", // 清空指定标签页的过滤请求列表



  // 请求头管理
  SET_REQUEST_HEADERS = "SET_REQUEST_HEADERS", // 设置请求头
  SET_REQUEST_HEADERS_FOR_EXTENSION = "SET_REQUEST_HEADERS_FOR_EXTENSION", // 为扩展页面设置请求头
  CLEANUP_REQUEST_HEADERS = "CLEANUP_REQUEST_HEADERS", // 清理请求头

  // 其他功能
  TOGGLE_SIDEPANEL = "TOGGLE_SIDEPANEL", // 切换侧边栏
  SEND_TO_TAB = "SEND_TO_TAB" // 向特定标签页发送消息
}

/**
 * 前端页面发送给插件的消息类型枚举
 * 用于 Downloader Page → Content Script → Background Script 通信
 */
export enum PageToExtensionMessageType {
  GET_DOWNLOAD_DATA_BY_ID = "GET_DOWNLOAD_DATA_BY_ID", // 获取指定ID的下载数据
  DOWNLOAD_FILE_WITH_HEADERS = "DOWNLOAD_FILE_WITH_HEADERS", // 设置请求头（不实际下载，由前端处理）
}

/**
 * 插件发送给前端页面的消息类型枚举
 * 用于 Background Script → Content Script → Downloader Page 通信
 */
export enum ExtensionToPageMessageType {
  CONTENT_SCRIPT_READY = "CONTENT_SCRIPT_READY", // 内容脚本准备就绪
  DOWNLOAD_DATA_RESPONSE = "DOWNLOAD_DATA_RESPONSE", // 下载数据响应
  DOWNLOAD_FILE_RESPONSE = "DOWNLOAD_FILE_RESPONSE", // 下载文件响应
  HEADERS_SET_COMPLETED = "HEADERS_SET_COMPLETED", // 请求头设置完成通知
}

/**
 * 连接管理消息类型枚举
 * 用于持久连接的状态更新
 */
export enum ConnectionMessageType {
  DATA_UPDATE = "DATA_UPDATE", // 数据更新通知
  PING = "PING", // 连接保活
}



/**
 * 消息接口
 */
export interface Message {
  type: BackgroundMessageType | PageToExtensionMessageType | ExtensionToPageMessageType | ConnectionMessageType
  payload?: any
  tabId?: number
  requestIds?: string[]
}

/**
 * 响应接口
 */
export interface Response {
  success: boolean
  data?: any
  message?: string
  error?: string
}


